import asyncio
from typing import Optional

import httpx

from app.config import (
    HTTP_CONNECT_TIMEOUT_SECONDS,
    HTTP_MAX_REDIRECTS,
    HTTP_POOL_CONNECTIONS,
    HTTP_POOL_MAXSIZE,
    HTTP_READ_TIMEOUT_SECONDS,
    HTTP_TIMEOUT_SECONDS,
)
from app.logging import logger


class HTTPClientManager:
    """Singleton HTTP client manager with connection pooling for optimal performance."""

    _instance: Optional["HTTPClientManager"] = None
    _client: Optional[httpx.AsyncClient] = None
    _lock = asyncio.Lock()

    def __new__(cls) -> "HTTPClientManager":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def get_client(self) -> httpx.AsyncClient:
        """Get or create the async HTTP client with connection pooling."""
        if (
            self._client is None
            or self._client.is_closed
            or not self._is_client_healthy()
        ):
            async with self._lock:
                if (
                    self._client is None
                    or self._client.is_closed
                    or not self._is_client_healthy()
                ):
                    if self._client is not None:
                        if self._client.is_closed:
                            logger.warning("HTTP client was closed, recreating...")
                        elif not self._is_client_healthy():
                            logger.warning(
                                "HTTP client appears unhealthy, recreating..."
                            )
                        # Close the old client properly
                        try:
                            await self._client.aclose()
                        except Exception as e:
                            logger.debug(f"Error closing old HTTP client: {e}")
                    await self._create_client()
        assert self._client is not None  # Should never be None after _create_client
        return self._client

    def _is_client_healthy(self) -> bool:
        """Check if the HTTP client is healthy and usable."""
        if self._client is None:
            return False

        try:
            # Check if the client has any underlying transport issues
            # This is a basic health check - we could expand this if needed
            return not self._client.is_closed
        except Exception:
            # If we can't even check the client state, it's not healthy
            return False

    async def _create_client(self) -> None:
        """Create a new HTTP client with optimized settings."""
        # Configure timeouts
        timeout = httpx.Timeout(
            connect=HTTP_CONNECT_TIMEOUT_SECONDS,
            read=HTTP_READ_TIMEOUT_SECONDS,
            write=HTTP_TIMEOUT_SECONDS,
            pool=HTTP_TIMEOUT_SECONDS,
        )

        # Configure connection limits for optimal pooling
        limits = httpx.Limits(
            max_keepalive_connections=HTTP_POOL_CONNECTIONS,
            max_connections=HTTP_POOL_MAXSIZE,
            keepalive_expiry=30.0,  # Keep connections alive for 30 seconds
        )

        # Create client with optimized settings
        self._client = httpx.AsyncClient(
            timeout=timeout,
            limits=limits,
            follow_redirects=True,
            max_redirects=HTTP_MAX_REDIRECTS,
            headers={
                "User-Agent": "Face-Validation-Service/1.0",
            },
        )

        logger.info(
            f"HTTP client initialized with connection pooling: "
            f"max_connections={HTTP_POOL_MAXSIZE}, "
            f"max_keepalive={HTTP_POOL_CONNECTIONS}, "
            f"timeout={HTTP_TIMEOUT_SECONDS}s"
        )

    async def close(self) -> None:
        """Close the HTTP client and clean up connections."""
        if self._client is not None:
            await self._client.aclose()
            self._client = None
            logger.info("HTTP client closed")

    async def __aenter__(self):
        """Async context manager entry."""
        return await self.get_client()

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        # Don't close the client here as it's a singleton
        # It will be closed during application shutdown
        # Suppress unused parameter warnings
        _ = exc_type, exc_val, exc_tb


# Global instance
_http_client_manager: Optional[HTTPClientManager] = None


def get_http_client_manager() -> HTTPClientManager:
    """Get the global HTTP client manager instance."""
    global _http_client_manager
    if _http_client_manager is None:
        _http_client_manager = HTTPClientManager()
    return _http_client_manager


async def get_http_client() -> httpx.AsyncClient:
    """Get the global HTTP client with connection pooling."""
    manager = get_http_client_manager()
    return await manager.get_client()


async def close_http_client() -> None:
    """Close the global HTTP client."""
    global _http_client_manager
    if _http_client_manager is not None:
        await _http_client_manager.close()
        _http_client_manager = None


async def make_http_request_with_retry(request_func, max_retries: int = 3):
    """
    Make an HTTP request with automatic client recreation on transport errors.

    Args:
        request_func: Async function that takes an httpx.AsyncClient and returns a response
        max_retries: Maximum number of retries for transport-level errors

    Returns:
        The response from the request function

    Raises:
        The last exception if all retries fail
    """
    last_exception = None

    for attempt in range(max_retries + 1):
        try:
            client = await get_http_client()
            return await request_func(client)
        except (
            ConnectionError,
            OSError,
            httpx.RemoteProtocolError,
            httpx.LocalProtocolError,
        ) as e:
            last_exception = e
            if attempt == max_retries:
                break

            logger.warning(
                f"HTTP transport error (attempt {attempt + 1}/{max_retries + 1}): {str(e)}. "
                f"Recreating client and retrying..."
            )

            # Force recreation of the HTTP client
            global _http_client_manager
            if _http_client_manager is not None:
                try:
                    await _http_client_manager.close()
                except Exception as close_error:
                    logger.debug(
                        f"Error closing HTTP client during retry: {close_error}"
                    )
                _http_client_manager = None

            # Small delay before retry
            await asyncio.sleep(0.1 * (attempt + 1))

    # If we get here, all retries failed
    if last_exception:
        raise last_exception
    else:
        raise Exception("All HTTP request retries failed")
