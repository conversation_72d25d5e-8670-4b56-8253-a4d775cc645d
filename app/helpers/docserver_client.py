"""
DocServer client for retrieving documents and files.
"""

import asyncio
import random
from functools import wraps
from typing import Any, Dict, Optional

import httpx

from app.config import (
    DOCSERVER_MAX_RETRIES,
    DOCSERVER_RETRY_BACKOFF_FACTOR,
    DOCSERVER_RETRY_MAX_DELAY,
    DOCSERVER_V2_BASEURL,
)
from app.logging import logger


def retry_with_backoff(
    max_retries: int = DOCSERVER_MAX_RETRIES,
    backoff_factor: float = DOCSERVER_RETRY_BACKOFF_FACTOR,
    max_delay: int = DOCSERVER_RETRY_MAX_DELAY,
):
    """
    Retry decorator with exponential backoff for async functions.

    Args:
        max_retries: Maximum number of retry attempts
        backoff_factor: Multiplier for exponential backoff
        max_delay: Maximum delay between retries in seconds
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except (
                    httpx.TimeoutException,
                    httpx.ConnectError,
                    httpx.NetworkError,
                ) as e:
                    last_exception = e
                    if attempt == max_retries:
                        break

                    # Calculate delay with exponential backoff and jitter
                    delay = min(
                        backoff_factor**attempt + random.uniform(0, 1), max_delay
                    )

                    logger.warning(
                        f"DocServer request failed (attempt {attempt + 1}/{max_retries + 1}): {str(e)}. "
                        f"Retrying in {delay:.1f}s..."
                    )
                    await asyncio.sleep(delay)
                except Exception as e:
                    # Don't retry on non-network errors (e.g., 4xx HTTP errors)
                    logger.error(
                        f"DocServer request failed with non-retryable error: {str(e)}"
                    )
                    raise e

            # If we get here, all retries failed
            logger.error(f"DocServer request failed after {max_retries + 1} attempts")
            if last_exception:
                raise last_exception
            else:
                raise Exception("All retry attempts failed")

        return wrapper

    return decorator


class DocServerClient:
    """Client for interacting with DocServer v2 API."""

    def __init__(self):
        """Initialize the DocServer client."""
        self.base_url = DOCSERVER_V2_BASEURL

    @retry_with_backoff()
    async def get_document_metadata(self, file_id: str) -> Optional[Dict[str, Any]]:
        """
        Get document metadata including MIME type.

        Args:
            file_id: The file ID to retrieve metadata for

        Returns:
            Dictionary containing document metadata or None if failed
        """
        try:
            # Import here to avoid circular imports
            from .http_client import get_http_client

            client = await get_http_client()
            response = await client.get(
                f"{self.base_url}/files/{file_id}?fieldsToReturn=mimeType"
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get document metadata for {file_id}: {str(e)}")
            return None

    @retry_with_backoff()
    async def download_document(self, file_id: str) -> Optional[bytes]:
        """
        Download document content as bytes.

        Args:
            file_id: The file ID to download

        Returns:
            Document content as bytes or None if failed
        """
        try:
            # Import here to avoid circular imports
            from .http_client import get_http_client

            client = await get_http_client()
            url_to_download = f"{self.base_url}/files/{file_id}?download=true"
            print(url_to_download)
            response = await client.get(url_to_download)
            response.raise_for_status()
            return response.content
        except Exception as e:
            logger.error(f"Failed to download document {file_id}: {str(e)}")
            return None

    @retry_with_backoff()
    async def patch_file_metadata(self, file_id: str, has_faces: bool) -> bool:
        """
        Update file metadata with face detection results.

        Args:
            file_id: The file ID to update
            has_faces: Whether faces were detected in the file

        Returns:
            True if the patch was successful, False otherwise
        """
        try:
            # Import here to avoid circular imports
            from .http_client import get_http_client

            client = await get_http_client()

            # Prepare the payload
            payload = {"_hasFaces": has_faces}

            # Make the PATCH request
            response = await client.patch(
                f"{self.base_url}/files/{file_id}?mergeArrayFields=true",
                json=payload,
                headers={"Content-Type": "application/json"},
            )
            response.raise_for_status()

            logger.info(
                f"Successfully updated file {file_id} with _hasFaces={has_faces}"
            )
            return True

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                logger.warning(f"File {file_id} not found in DocServer")
            else:
                logger.error(
                    f"HTTP error updating file {file_id}: {e.response.status_code} - {str(e)}"
                )
            return False
        except Exception as e:
            logger.error(f"Failed to update file metadata for {file_id}: {str(e)}")
            return False


# Global client instance
docserver_client = DocServerClient()


# Convenience functions for backward compatibility
async def get_document_metadata(file_id: str) -> Optional[Dict[str, Any]]:
    """Get document metadata."""
    return await docserver_client.get_document_metadata(file_id)


async def download_document(file_id: str) -> Optional[bytes]:
    """Download document content."""
    return await docserver_client.download_document(file_id)


async def patch_file_metadata(file_id: str, has_faces: bool) -> bool:
    """Update file metadata with face detection results."""
    return await docserver_client.patch_file_metadata(file_id, has_faces)
