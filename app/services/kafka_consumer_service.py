"""
Kafka consumer service for processing file creation events.
Optimized for connection resilience and proper error handling.
"""

import asyncio
import concurrent.futures
import json
import threading
import time
from typing import Optional

from kafka import KafkaConsumer
from kafka.errors import KafkaError

from app.config import (
    KAFKA_AUTO_OFFSET_RESET,
    KAFKA_CONSUMER_GROUP,
    KAFKA_ENABLED,
    KAFKA_TOPIC_ES_CREATED_FILE,
    KAFKA_URL,
)
from app.helpers import download_document
from app.logging import logger
from app.models import FileMessage
from app.services.face_validation_service import face_validation_service


class KafkaConsumerService:
    """Optimized Kafka consumer service with connection resilience."""

    def __init__(self):
        """Initialize the Kafka consumer service."""
        self.consumer: Optional[KafkaConsumer] = None
        self.is_running = False
        self.consumer_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()
        self._processing_tasks: set = set()  # Track active processing tasks
        self._executor: Optional[concurrent.futures.ThreadPoolExecutor] = None

        # Supported image MIME types
        self.image_mimetypes = {"image/jpeg", "image/pjpeg", "image/x-png", "image/png"}

    def _create_consumer(self) -> Optional[KafkaConsumer]:
        """
        Create a new Kafka consumer with optimized settings.

        Returns:
            KafkaConsumer instance or None if creation failed
        """
        try:
            consumer = KafkaConsumer(
                KAFKA_TOPIC_ES_CREATED_FILE,
                bootstrap_servers=KAFKA_URL,
                group_id=KAFKA_CONSUMER_GROUP,
                auto_offset_reset=KAFKA_AUTO_OFFSET_RESET,
                enable_auto_commit=True,
                value_deserializer=lambda x: x.decode("utf-8"),
                # Optimized settings for resilience
                consumer_timeout_ms=1000,  # 1 second timeout for polling
                session_timeout_ms=30000,  # 30 seconds session timeout
                heartbeat_interval_ms=10000,  # 10 seconds heartbeat
                max_poll_records=10,  # Process in small batches
                retry_backoff_ms=1000,  # 1 second retry backoff
            )

            logger.info(
                f"[KAFKA] Consumer created for topic: {KAFKA_TOPIC_ES_CREATED_FILE}"
            )
            return consumer

        except Exception as e:
            logger.error(f"[KAFKA] Failed to create consumer: {str(e)}")
            return None

    async def _process_file_message(self, message_data: FileMessage) -> None:
        """
        Process a file message for face validation.

        Args:
            message_data: Parsed file message from Kafka
        """
        # Check if shutdown is in progress
        if self._shutdown_event.is_set():
            logger.debug("[KAFKA] Skipping message processing - shutdown in progress")
            return

        if not message_data.payload.es_id:
            logger.warning("[KAFKA] Skipping message - missing es_id")
            return

        file_id = message_data.payload.es_id
        file_type = message_data.payload.file_type

        # Only process image files
        if file_type != "image":
            logger.debug(
                f"[KAFKA] Skipping non-image file [{file_id}]: type={file_type}"
            )
            return

        try:
            # Check again if shutdown is in progress before starting processing
            if self._shutdown_event.is_set():
                logger.debug(
                    f"[KAFKA] Aborting processing of {file_id} - shutdown in progress"
                )
                return

            # Download the file content
            logger.info(f"[KAFKA] Processing image file: {file_id}")
            file_bytes = await download_document(file_id)

            if file_bytes is None:
                logger.error(f"[KAFKA] Failed to download file: {file_id}")
                return

            # Check once more before face validation
            if self._shutdown_event.is_set():
                logger.debug(
                    f"[KAFKA] Aborting face validation of {file_id} - shutdown in progress"
                )
                return

            # Validate faces in the image
            result = await face_validation_service.validate_face_from_bytes(
                file_bytes, file_id, source="kafka"
            )

            # Log the result
            if result.error:
                logger.error(
                    f"[KAFKA] Face validation failed [{file_id}]: {result.error}"
                )
            else:
                status = "HAS_FACES" if result.has_face else "NO_FACES"
                logger.info(f"[KAFKA] Face validation result [{file_id}]: {status}")

        except Exception as e:
            # Don't log errors if shutdown is in progress
            if not self._shutdown_event.is_set():
                logger.error(f"[KAFKA] Error processing file [{file_id}]: {str(e)}")
            else:
                logger.debug(
                    f"[KAFKA] Processing interrupted during shutdown for {file_id}: {str(e)}"
                )

    def _run_async_task(self, coro):
        """
        Run an async coroutine in a new event loop.
        This is used to run async tasks from the sync Kafka consumer thread.
        """
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(coro)
        except Exception as e:
            if not self._shutdown_event.is_set():
                logger.error(f"[KAFKA] Error in async task: {str(e)}")
        finally:
            try:
                loop.close()
            except:
                pass

    def _consumer_loop(self) -> None:
        """Main consumer loop that runs in a separate thread."""
        logger.info("[KAFKA] Consumer loop started")

        while not self._shutdown_event.is_set():
            try:
                # Create consumer if not exists or if connection is lost
                if self.consumer is None:
                    self.consumer = self._create_consumer()
                    if self.consumer is None:
                        logger.error(
                            "[KAFKA] Failed to create consumer, retrying in 5 seconds..."
                        )
                        time.sleep(5)
                        continue

                # Poll for messages with timeout
                message_batch = self.consumer.poll(timeout_ms=1000)

                if not message_batch:
                    continue  # No messages, continue polling

                # Process messages
                for topic_partition, messages in message_batch.items():
                    for message in messages:
                        if self._shutdown_event.is_set():
                            break

                        try:
                            # Parse message
                            data = FileMessage(**json.loads(message.value))

                            # Process in async context using thread pool executor
                            if self._executor is None:
                                self._executor = concurrent.futures.ThreadPoolExecutor(
                                    max_workers=2, thread_name_prefix="kafka-async"
                                )

                            # Submit async task to executor
                            future = self._executor.submit(
                                self._run_async_task, self._process_file_message(data)
                            )

                            # Track the task for graceful shutdown
                            self._processing_tasks.add(future)

                            # Clean up completed tasks
                            self._processing_tasks = {
                                task
                                for task in self._processing_tasks
                                if not task.done()
                            }

                        except json.JSONDecodeError:
                            logger.warning(
                                f"[KAFKA] Invalid JSON message: {message.value}"
                            )
                        except Exception as e:
                            logger.error(f"[KAFKA] Error processing message: {str(e)}")

            except KafkaError as e:
                logger.error(f"[KAFKA] Kafka error: {str(e)}")
                # Close and recreate consumer on Kafka errors
                self._close_consumer()
                time.sleep(5)  # Wait before retrying

            except Exception as e:
                logger.error(f"[KAFKA] Unexpected error in consumer loop: {str(e)}")
                time.sleep(1)  # Brief pause before continuing

        logger.info("[KAFKA] Consumer loop stopped")

    def _close_consumer(self) -> None:
        """Close the current consumer connection."""
        if self.consumer:
            try:
                self.consumer.close()
                logger.info("[KAFKA] Consumer connection closed")
            except Exception as e:
                logger.warning(f"[KAFKA] Error closing consumer: {str(e)}")
            finally:
                self.consumer = None

    def start(self) -> bool:
        """
        Start the Kafka consumer service.

        Returns:
            True if started successfully, False otherwise
        """
        if not KAFKA_ENABLED:
            logger.info("[KAFKA] Kafka consumer disabled by configuration")
            return False

        if self.is_running:
            logger.warning("[KAFKA] Consumer service already running")
            return True

        try:
            self.is_running = True
            self._shutdown_event.clear()

            # Start consumer in separate thread
            self.consumer_thread = threading.Thread(
                target=self._consumer_loop, name="kafka-consumer", daemon=True
            )
            self.consumer_thread.start()

            logger.info("[KAFKA] Consumer service started successfully")
            return True

        except Exception as e:
            logger.error(f"[KAFKA] Failed to start consumer service: {str(e)}")
            self.is_running = False
            return False

    def stop(self) -> None:
        """Stop the Kafka consumer service."""
        if not self.is_running:
            return

        logger.info("[KAFKA] Stopping consumer service...")

        # Signal shutdown
        self._shutdown_event.set()
        self.is_running = False

        # Close consumer
        self._close_consumer()

        # Wait for active processing tasks to complete
        if self._processing_tasks:
            logger.info(
                f"[KAFKA] Waiting for {len(self._processing_tasks)} active tasks to complete..."
            )
            for task in list(self._processing_tasks):
                try:
                    task.result(timeout=5)  # Wait up to 5 seconds per task
                except concurrent.futures.TimeoutError:
                    logger.warning("[KAFKA] Task did not complete within timeout")
                except Exception as e:
                    logger.debug(f"[KAFKA] Task completed with error: {str(e)}")
            self._processing_tasks.clear()

        # Shutdown executor
        if self._executor:
            logger.info("[KAFKA] Shutting down task executor...")
            self._executor.shutdown(wait=True)
            self._executor = None

        # Wait for thread to finish
        if self.consumer_thread and self.consumer_thread.is_alive():
            self.consumer_thread.join(timeout=10)
            if self.consumer_thread.is_alive():
                logger.warning("[KAFKA] Consumer thread did not stop gracefully")

        logger.info("[KAFKA] Consumer service stopped")

    def is_healthy(self) -> bool:
        """
        Check if the consumer service is healthy.

        Returns:
            True if healthy, False otherwise
        """
        return (
            self.is_running
            and self.consumer_thread is not None
            and self.consumer_thread.is_alive()
        )


# Global service instance
kafka_consumer_service = KafkaConsumerService()
