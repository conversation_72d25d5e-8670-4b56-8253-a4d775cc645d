#!/usr/bin/env python3
"""
Test script to verify HTTP client fixes for TCPTransport errors.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.helpers.http_client import get_http_client, make_http_request_with_retry, close_http_client
from app.helpers.docserver_client import docserver_client
from app.logging import logger


async def test_http_client_recreation():
    """Test that HTTP client can be recreated after transport errors."""
    print("Testing HTTP client recreation...")
    
    try:
        # Get initial client
        client1 = await get_http_client()
        print(f"Initial client: {id(client1)}")
        
        # Force close the client to simulate transport error
        await client1.aclose()
        print("Closed client to simulate transport error")
        
        # Get client again - should create a new one
        client2 = await get_http_client()
        print(f"New client: {id(client2)}")
        
        # Verify they are different instances
        if id(client1) != id(client2):
            print("✓ HTTP client recreation works correctly")
            return True
        else:
            print("✗ HTTP client was not recreated")
            return False
            
    except Exception as e:
        print(f"✗ Error testing HTTP client recreation: {e}")
        return False


async def test_robust_request_function():
    """Test the make_http_request_with_retry function."""
    print("\nTesting robust HTTP request function...")
    
    try:
        async def test_request(client):
            # Make a simple request to a reliable endpoint
            response = await client.get("https://httpbin.org/status/200")
            return response.status_code
        
        status_code = await make_http_request_with_retry(test_request)
        
        if status_code == 200:
            print("✓ Robust HTTP request function works correctly")
            return True
        else:
            print(f"✗ Unexpected status code: {status_code}")
            return False
            
    except Exception as e:
        print(f"✗ Error testing robust request function: {e}")
        return False


async def test_docserver_client_robustness():
    """Test that docserver client handles errors gracefully."""
    print("\nTesting docserver client robustness...")
    
    try:
        # Test with a non-existent file ID
        result = await docserver_client.get_document_metadata("nonexistent_file_id")
        
        # Should return None for non-existent files, not crash
        if result is None:
            print("✓ DocServer client handles errors gracefully")
            return True
        else:
            print(f"✗ Unexpected result: {result}")
            return False
            
    except Exception as e:
        print(f"✗ Error testing docserver client: {e}")
        return False


async def main():
    """Run all tests."""
    print("Running HTTP client fixes tests...\n")
    
    tests = [
        test_http_client_recreation,
        test_robust_request_function,
        test_docserver_client_robustness,
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Clean up
    await close_http_client()
    
    # Summary
    passed = sum(results)
    total = len(results)
    print(f"\nTest Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! HTTP client fixes are working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
